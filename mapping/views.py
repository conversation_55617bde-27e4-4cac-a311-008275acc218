from django.contrib.auth.decorators import login_required
from django.contrib.gis.geos import GEOSGeometry
from django.shortcuts import render, redirect
from django.http import JsonResponse
import json
from django.views.decorators.csrf import csrf_protect, ensure_csrf_cookie
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import authentication_classes, permission_classes
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import authentication_classes, permission_classes
from django.http import HttpResponse, Http404
from .models import Hubs, Layers, Departments, Region, DepartmentType, UserRegionAccess
from django.db.models import Q
from django.conf import settings
import os

@login_required
def get_layer_polygons(request):
    layer_id = request.GET.get('layer_id')  # Получаем параметр layer_id из запроса
    ayer_id = request.GET.get('layer_id')
    region_id = request.GET.get('region_id')

    polygons = Hubs.objects.filter(
        layers_id=layer_id,
        department__region=region_id,
        department__status__in=[0, 1, 2, 3]
    ).values(
        'id',
        'polygon',
        'department__name',
        'department__lat',
        'department__lon',
        'department__status'
    )
    
    region= Region.objects.get(id = region_id)

    polygons_list = []
    for polygon in polygons:
        actual_geometry = GEOSGeometry(polygon['polygon'])  # Предполагая, что полигон хранится в формате HEX
        geometry_dict = json.loads(actual_geometry.geojson)  # Преобразование геометрии в JSON-совместимый словарь

        polygons_list.append({
            'id': polygon['id'],
            'geometry': geometry_dict,
            'departament': polygon['department__name'],
            'lat': polygon['department__lat'],
            'lon': polygon['department__lon'],
            'status': polygon['department__status'],
        })
    
    result={"polygons": polygons_list, "region_lat":region.lat, "region_lon":region.lon}

    return JsonResponse(result, safe=False)


@login_required
def layer_map(request):
    layers = Layers.objects.all()  # Получите список слоев из базы данных
    regions = Region.objects.all()

    user_region_access_list = UserRegionAccess.objects.filter(user=request.user)

    region_with_access = []
    for user_access in user_region_access_list:
        if user_access.has_full_access:
            # Если есть полный доступ, добавляем все категории
            region_with_access.extend(Region.objects.all())
            break  # Прерываем цикл, так как полный доступ есть к любой категории
        else:
            # Если нет полного доступа, добавляем только категорию, к которой есть доступ
            region_with_access.append(user_access.region)
    
    return render(request, 'layer_map.html', {'layers': layers, 'regions': region_with_access})


@login_required
def get_departments(request):
    region_id = request.GET.get('region_id')
    departmens = Departments.objects.filter(status__in=[1], region=region_id, lat__isnull=False, lon__isnull=False ).values('id', 'name', 'lat', 'lon', 'status')

    departmens_list = []
    for dep in departmens:
        departmens_list.append({
            'id': dep['id'],
            'name': dep['name'],
            'lat': dep['lat'],
            'lon': dep['lon'],
            'status': dep['status']
        })

    return JsonResponse(departmens_list, safe=False)


@login_required
def save_polygon(request):
    body = request.body
    str_json = body.decode('utf8').replace("'", '"')
    data = json.loads(str_json)

    departments = data['department']
    layer = data['layer']
    geometry = data['geometry']['coordinates'][0]

    polygon = GEOSGeometry(json.dumps({
        "type": "Polygon",
        "coordinates": [geometry]
    }))

    # Сохраните полигон и другие данные в базу данных
    polygon_data = Hubs(polygon=polygon, layers_id=layer, department_id=departments)
    polygon_data.save()
    return JsonResponse({"status": "Polygon saved successfully"}, safe=False)


@login_required
def bulk_save_hubs(request):
    """Массовое сохранение хабов с защитой для прода"""
    if request.method != 'POST':
        return JsonResponse({"error": "Only POST method allowed"}, status=405)

    # Проверка прав доступа - только для staff пользователей
    if not request.user.is_staff:
        return JsonResponse({"error": "Access denied. Staff only."}, status=403)

    try:
        body = request.body
        str_json = body.decode('utf8').replace("'", '"')
        data = json.loads(str_json)

        # Получаем данные
        changed_hubs = data.get('changed_hubs', [])
        test_mode = data.get('test_mode', True)  # По умолчанию тестовый режим

        results = {
            'success': True,
            'test_mode': test_mode,
            'processed': {
                'updated': 0,
                'errors': []
            }
        }

        # ТЕСТОВЫЙ РЕЖИМ - только проверяем данные, не сохраняем
        if test_mode:
            results['message'] = 'ТЕСТОВЫЙ РЕЖИМ - изменения НЕ сохранены'

            # Проверяем существование хабов для обновления
            for hub_data in changed_hubs:
                hub_id = hub_data.get('id')
                if hub_id:
                    try:
                        hub = Hubs.objects.get(id=hub_id)
                        results['processed']['updated'] += 1
                    except Hubs.DoesNotExist:
                        results['processed']['errors'].append(f'Хаб ID {hub_id} не найден')
                else:
                    results['processed']['errors'].append('Отсутствует ID хаба для обновления')

        # РЕАЛЬНЫЙ РЕЖИМ - сохраняем изменения
        else:
            results['message'] = 'РЕАЛЬНЫЙ РЕЖИМ - изменения сохранены в базу'

            # Обновляем существующие хабы (ТОЛЬКО обновление геометрии, НЕ удаление!)
            for hub_data in changed_hubs:
                hub_id = hub_data.get('id')
                geometry_data = hub_data.get('geometry')

                if not hub_id or not geometry_data:
                    results['processed']['errors'].append('Неполные данные хаба')
                    continue

                try:
                    hub = Hubs.objects.get(id=hub_id)

                    # Создаем новую геометрию
                    polygon = GEOSGeometry(json.dumps(geometry_data))
                    hub.polygon = polygon
                    hub.save()

                    results['processed']['updated'] += 1

                except Hubs.DoesNotExist:
                    results['processed']['errors'].append(f'Хаб ID {hub_id} не найден')
                except Exception as e:
                    results['processed']['errors'].append(f'Ошибка обновления хаба {hub_id}: {str(e)}')

        return JsonResponse(results)

    except json.JSONDecodeError:
        return JsonResponse({"error": "Invalid JSON data"}, status=400)
    except Exception as e:
        return JsonResponse({"error": f"Server error: {str(e)}"}, status=500)


@login_required
def get_points_outside_hubs(request):
    """Получает точки из geo_build, которые НЕ попадают в полигоны хабов И находятся в пределах выбранного региона"""
    from django.db import connection

    # Получаем параметры фильтрации
    region_id = request.GET.get('region_id')
    layer_id = request.GET.get('layer_id')

    try:
        with connection.cursor() as cursor:
            if region_id or layer_id:
                # ОПТИМИЗИРОВАННАЯ ФИЛЬТРАЦИЯ: Используем границы регионов вместо радиуса от отделений
                base_query = """
                             SELECT gb.build_id, ST_X(gb.geom_point), ST_Y(gb.geom_point)
                             FROM adr.geo_build gb
                             WHERE gb.geom_point IS NOT NULL
                             """

                params = []

                # Фильтрация по региону: берем точки ВНУТРИ границ региона
                if region_id:
                    base_query += """
                    AND EXISTS (
                        SELECT 1 FROM region_boundaries rb
                        WHERE rb.region_id = %s
                        AND ST_Contains(rb.boundary_polygon, gb.geom_point)
                    )
                    """
                    params.append(region_id)

                # Дополнительная фильтрация по слою
                if layer_id:
                    base_query += """
                    AND EXISTS (
                        SELECT 1 FROM hubs h
                        WHERE h.layers_id = %s
                        AND h.polygon IS NOT NULL
                        AND ST_DWithin(gb.geom_point, h.polygon, 0.1)
                    )
                    """
                    params.append(layer_id)
            else:
                # Если НЕ выбран регион/слой - показываем случайные точки для демонстрации
                base_query = """
                             SELECT gb.build_id, ST_X(gb.geom_point), ST_Y(gb.geom_point)
                             FROM adr.geo_build gb
                             WHERE gb.geom_point IS NOT NULL
                             ORDER BY RANDOM()
                             """
                params = []

            base_query += " LIMIT 1000"

            cursor.execute(base_query, params)

            results = cursor.fetchall()

            print(f"🔍 ОТЛАДКА ФИЛЬТРАЦИИ:")
            print(f"   region_id: {region_id}")
            print(f"   layer_id: {layer_id}")
            print(f"   Найдено точек в базе: {len(results)}")

            # Преобразуем в список словарей с автоисправлением координат
            points = []
            corrected_count = 0

            for row in results:
                st_x = float(row[1]) if row[1] else None  # ST_X из базы
                st_y = float(row[2]) if row[2] else None  # ST_Y из базы

                if st_x is None or st_y is None:
                    continue

                # Изначально используем координаты как было: ST_X=lat, ST_Y=lon
                lat = st_x
                lon = st_y

                # НАОБОРОТ: Проверяем находится ли точка ВНЕ Казахстана с текущими координатами
                # Казахстан: lat 40-56, lon 46-87
                if not (40.0 <= lat <= 56.0 and 46.0 <= lon <= 87.0):
                    # Точка ВНЕ Казахстана - оставляем как есть (не трогаем)
                    pass
                else:
                    # Точка В Казахстане - меняем координаты местами
                    lat = st_y  # ST_Y как latitude
                    lon = st_x  # ST_X как longitude
                    corrected_count += 1

                points.append({
                    'id': row[0],
                    'lat': lat,
                    'lon': lon
                })

            # Фильтруем точки с валидными координатами
            valid_points = [p for p in points if p['lat'] and p['lon']]

            print(f"   Исправлено координат: {corrected_count} из {len(points)}")
            print(f"   Валидных точек: {len(valid_points)}")

            return JsonResponse({
                'success': True,
                'points': valid_points,
                'total_count': len(valid_points),
                'corrected_count': corrected_count,
                'message': f'Найдено {len(valid_points)} точек в регионе. Исправлено координат: {corrected_count}'
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Ошибка выполнения запроса: {str(e)}',
            'points': []
        }, status=500)


@method_decorator(ensure_csrf_cookie, name='dispatch')
@authentication_classes([])
@permission_classes([])
class UpdateDepartment(APIView):
    def post(self, request, *args, **kwargs):
        lat = None
        lon = None
        body = request.body
        str_json = body.decode('utf8').replace("'", '"')
        data = json.loads(str_json)

        dep = data['dep-id']

        region_id = None
        dep_type = None
        class_field = None
        sub_department_id=None

        if 'class' in data:
            class_field=data.get('class', None)

        if 'region' in data:
            try:
                region_id = Region.objects.get(name=data['region'])
            except Region.DoesNotExist:
                print("REGION NOT FOUND")

        if 'type' in data:
            try:
                dep_type = DepartmentType.objects.get(code=data['type'])
            except DepartmentType.DoesNotExist:
                print("dep_type NOT FOUND")

        if 'sub_dep_id' in data:
            try:
                if data['sub_dep_id']==None:
                    sub_department_id=None
                else:
                    sub_department_id=Departments.objects.get(id=data['sub_dep_id'])
            except Departments.DoesNotExist:
                print("SUB Departments NOT FOUND")

        geo = data.get('geo', None)
        if geo:
            geo_data = geo.split(',')
            lat = geo_data[0].strip()
            lon = geo_data[1].strip()

        search_params = {'id': dep}

        update_values = {}
        possible_fields = ['name', 'code', 'post_code', 'new_post_code', 'status',  'location_name','dm_tindex']
        for field in possible_fields:
            if field in data:
                update_values[field] = data[field]

        if lat is not None:
            update_values['lat'] = lat
        if lon is not None:
            update_values['lon'] = lon

        if region_id is not None:
            update_values['region'] = region_id
        if dep_type is not None:
            update_values['type'] = dep_type

        if class_field is not None:
            update_values['class_field'] = class_field

        update_values['sub_department']=sub_department_id

        department, created = Departments.objects.update_or_create(defaults=update_values, **search_params)

        print(department, created)
        if created:
            res_message = "created"
        else:
            res_message = "updated"

        return JsonResponse({"status": "Department successfully " + res_message}, safe=False)
    
import mimetypes
def download_file(request, filename):
    file_path = os.path.join(settings.STATIC_ROOT, filename)

    if os.path.exists(file_path):
        # Get the MIME type for the file
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type is None:
            mime_type = 'application/octet-stream'

        with open(file_path, 'rb') as f:
            response = HttpResponse(f.read(), content_type=mime_type)
            response['Content-Disposition'] = 'attachment; filename="KAZ_POST2023-24RU.pdf"'
            return response
    else:
        raise Http404("File does not exist")

from django.contrib.auth import logout, authenticate, login
from django.http import HttpResponseRedirect
from django.urls import reverse

def CustomLogoutView(request):
    logout(request)
    return HttpResponseRedirect(request.META.get('HTTP_REFERER', reverse('layer_map')))


from .forms import MyAuthenticationForm

def login_view(request):
    if request.method == 'POST':
        form = MyAuthenticationForm(data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                return redirect('layer_map')  # Замените 'home' на вашу страницу назначения
            else:
                form.add_error(None, 'Invalid username or password.')
    else:
        form = MyAuthenticationForm()
    
    return render(request, 'login2.html', {'form': form})