from django.contrib.gis.db import models as geo_models
from django.contrib.gis.geos import Point
from django.db import models
from django.db.models.signals import post_delete, post_save

from changelog.mixins import ChangeloggableMixin
from changelog.signals import journal_save_handler, journal_delete_handler

from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _


class Entity(models.Model):
    created = models.DateTimeField(
        auto_now_add=True, help_text=_('created'),
        verbose_name=_('created'),
    )
    modified = models.DateTimeField(
        auto_now=True, help_text=_('modified'),
        verbose_name=_('modified'),
    )

    class Meta:
        abstract = True


class EntitledEntity(Entity):
    class Meta:
        abstract = True


class HubQuerySet(models.QuerySet):

    def get_hub(self, longitude, latitude, client_id):
        point = Point(float(longitude), float(latitude))
        return self.filter(
            polygon__contains=point, is_active=True,
            delivery_service__client_id=client_id,
        ).first()



class Layers(models.Model):
    id = models.AutoField(primary_key=True)
    code = models.CharField(max_length=255, blank=True, null=False)
    name = models.CharField(max_length=255, blank=True, null=False)

    class Meta:
        verbose_name = _('Слой')
        verbose_name_plural = _('Слои')
        db_table = 'layers'

    def __str__(self):
        return str(self.name)

    def __unicode__(self):
        return self.name

class Region(models.Model):
    id = models.IntegerField(primary_key=True)
    name = models.CharField(max_length=100, blank=True, null=True)
    lat = models.FloatField(blank=True, null=True)
    lon = models.FloatField(blank=True, null=True)

    class Meta:
        verbose_name = _('Регион')
        verbose_name_plural = _('Регионы')
        db_table = 'region'
    def __str__(self):
        return str(self.name)

class DepartmentType(models.Model):
    id = models.IntegerField(primary_key=True)
    code = models.CharField(max_length=50)

    class Meta:
        managed = False
        db_table = 'department_type'
        verbose_name = _('Тип отделения')
        verbose_name_plural = _('Типы отделений')
    def __str__(self):
        return str(self.code)
        
class Departments(models.Model):
    id = models.IntegerField(primary_key=True)
    name = models.CharField(max_length=100, blank=True, null=True)
    code = models.CharField(max_length=50, blank=True, null=True)
    post_code = models.CharField(max_length=15, blank=True, null=True)
    new_post_code = models.CharField(max_length=50, blank=True, null=True)
    lat = models.FloatField(blank=True, null=True)
    lon = models.FloatField(blank=True, null=True)
    status = models.IntegerField(blank=True, null=True)
    region = models.ForeignKey(Region, models.DO_NOTHING, blank=True, null=True)
    type = models.ForeignKey(DepartmentType, models.DO_NOTHING, db_column='type', blank=True, null=True)
    class_field = models.CharField(db_column='class', max_length=5, blank=True, null=True)
    location_name = models.CharField(max_length=255, blank=True, null=True)
    dm_tindex = models.CharField(blank=True, null=True, default="------", verbose_name="Технологический index",max_length=7 )
    sub_department = models.ForeignKey('self', models.DO_NOTHING, blank=True, null=True, verbose_name="замещающее отделение")
    
    class Meta:
        verbose_name = _('Отделение')
        verbose_name_plural = _('Отделения')
        db_table = 'department'
    def __str__(self):
        return str(self.name)





class HubsMapEditor(models.Model):
    """Модель-заглушка для ссылки на редактор карты в админке"""

    class Meta:
        managed = False  # Не создавать таблицу в БД
        verbose_name = "Редактор карты хабов"
        verbose_name_plural = "🗺️ Групповое редактирование полигонов"
        app_label = 'mapping'

    def __str__(self):
        return self.name


class Hubs(ChangeloggableMixin, EntitledEntity):
    polygon = geo_models.PolygonField(_('polygon'), null=True)
    is_active = models.BooleanField(default=True)
    department = models.ForeignKey(Departments, models.DO_NOTHING, blank=True, null=True)
    layers = models.ForeignKey(Layers, models.DO_NOTHING, blank=True, null=True)

    class Meta:
        verbose_name = _('Полигон')
        verbose_name_plural = _('Полигоны')
        db_table = 'hubs'

    def __str__(self):
        return str(self.department.name)
    
post_save.connect(journal_save_handler, sender=Hubs)
post_delete.connect(journal_delete_handler, sender=Hubs)
    


class Auto(models.Model):
    equipment_num = models.CharField(max_length=18, blank=True, null=True)
    name_tech_object = models.CharField(max_length=40, blank=True, null=True)
    sing_of_ownership = models.CharField(max_length=1, blank=True, null=True)
    date_change = models.CharField(max_length=8, blank=True, null=True)
    manufacture_type = models.CharField(max_length=20, blank=True, null=True)
    manufacture_date = models.CharField(max_length=4, blank=True, null=True)
    inventory_number = models.CharField(max_length=25, blank=True, null=True)
    branch = models.CharField(max_length=4, blank=True, null=True)
    tech_place = models.CharField(max_length=25, blank=True, null=True)
    parent_tech_place = models.CharField(max_length=25, blank=True, null=True)
    cost_center = models.CharField(max_length=10, blank=True, null=True)
    license_num = models.CharField(max_length=15, blank=True, null=True)
    vin = models.CharField(max_length=30, blank=True, null=True)
    imei = models.CharField(max_length=18, blank=True, null=True)
    kind = models.CharField(max_length=10, blank=True, null=True)
    load_weight = models.CharField(max_length=13, blank=True, null=True)
    weight_unit = models.CharField(max_length=3, blank=True, null=True)
    load_volume = models.CharField(max_length=13, blank=True, null=True)
    volume_unit = models.CharField(max_length=3, blank=True, null=True)
    engine_type = models.CharField(max_length=10, blank=True, null=True)
    engine_capacity = models.CharField(max_length=13, blank=True, null=True)
    engice_capacity_unit = models.CharField(max_length=3, blank=True, null=True)
    category = models.CharField(max_length=20, blank=True, null=True)
    fuel_consumption_rates = models.CharField(max_length=30, blank=True, null=True)
    status = models.CharField(max_length=30, blank=True, null=True)
    manufacturer = models.CharField(max_length=30, blank=True, null=True)
    stats = models.CharField(max_length=20, blank=True, null=True)
    skif_id = models.UUIDField(blank=True, null=True)
    last_lon = models.DecimalField(max_digits=1000, decimal_places=1000, blank=True, null=True)
    last_lat = models.DecimalField(max_digits=1000, decimal_places=1000, blank=True, null=True)
    date_point = models.DateTimeField(blank=True, null=True)
    date_update_coodr = models.DateTimeField(blank=True, null=True)

    class Meta:
        verbose_name = _('Транспорт')
        verbose_name_plural = _('Транспорт')
        db_table = 'auto'


class ProductCategoryLayers(models.Model):
    product_code = models.CharField(max_length=20,primary_key=True, verbose_name='Код продукта')
    product_name = models.CharField(max_length=100, verbose_name='Наименование продукта')
    layer = models.ForeignKey(Layers, models.DO_NOTHING, blank=True, null=True, verbose_name='Слои')

    class Meta:
        verbose_name = _('Продукты по слоям')
        verbose_name_plural = _('Продукты по слоям')
        db_table = 'adr"."product_category_layer'

    def __str__(self):
        return str(self.product_code)

    def __unicode__(self):
        return self.product_code
    
class UserRegionAccess(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    region = models.ForeignKey(Region, blank=True, on_delete=models.CASCADE, verbose_name='Регион')
    has_full_access = models.BooleanField(default=False, verbose_name='Полный доступ')

    class Meta:
        verbose_name = _('Доступы по регионам')
        verbose_name_plural = _('Доступы по регионам')
        db_table = 'user_region_access'

    def __str__(self):
        return str(self.user)

    def __unicode__(self):
        return self.user


class RegionBoundary(models.Model):
    """Границы регионов для точной фильтрации точек"""
    region = models.ForeignKey(Region, on_delete=models.CASCADE, verbose_name='Регион')
    region_name = models.CharField(max_length=100, verbose_name='Название региона')
    boundary_polygon = geo_models.PolygonField(verbose_name='Полигон границы', srid=4326)
    osm_type = models.CharField(max_length=20, blank=True, null=True, verbose_name='Тип OSM')
    osm_id = models.BigIntegerField(blank=True, null=True, verbose_name='ID OSM')
    admin_level = models.IntegerField(blank=True, null=True, verbose_name='Административный уровень')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='Дата создания')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='Дата обновления')

    class Meta:
        verbose_name = _('Граница региона')
        verbose_name_plural = _('Границы регионов')
        db_table = 'region_boundaries'
        unique_together = ['region', 'osm_id']

    def __str__(self):
        return f"Граница {self.region_name}"